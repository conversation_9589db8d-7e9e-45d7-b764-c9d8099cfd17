import {
  Animated,
  PanResponder,
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useRef } from 'react';

const SCREEN_WIDTH = Dimensions.get('window').width;
const TRACK_WIDTH = SCREEN_WIDTH - 40;
const THUMB_SIZE = 50;
const TRIGGER_THRESHOLD = TRACK_WIDTH - THUMB_SIZE;

type SwipeButtonProps = {
  onSwipeComplete: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  label?: string;
  thumbIcon?: JSX.Element;
  trackColor?: string;
  fillColor?: string;
  textColor?: string;
  labelStyle?: TextStyle;
  containerStyle?: ViewStyle;
};

export function SwipeButton({
  onSwipeComplete,
  isLoading = false,
  disabled = false,
  label = 'Desliza para confirmar',
  thumbIcon,
  trackColor = '#0050C7',
  fillColor = '#007AFF',
  textColor = '#FFFFFF',
  labelStyle,
  containerStyle,
}: SwipeButtonProps) {
  const translateX = useRef(new Animated.Value(0)).current;

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) =>
        !disabled && gestureState.dx > 5,
      onPanResponderMove: (_, gesture) => {
        if (gesture.dx >= 0 && gesture.dx <= TRIGGER_THRESHOLD) {
          translateX.setValue(gesture.dx);
        }
      },
      onPanResponderRelease: (_, gesture) => {
        if (gesture.dx > TRIGGER_THRESHOLD - 10) {
          onSwipeComplete?.();
          Animated.timing(translateX, {
            toValue: 0,
            duration: 300,
            useNativeDriver: false,
          }).start();
        } else {
          Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: false,
          }).start();
        }
      },
    })
  ).current;

  const fillWidth = translateX.interpolate({
    inputRange: [0, TRIGGER_THRESHOLD],
    outputRange: [THUMB_SIZE, TRACK_WIDTH],
    extrapolate: 'clamp',
  });

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={[styles.track, { backgroundColor: trackColor }]}>
        {!isLoading && (
          <Animated.View
            style={[
              styles.fill,
              { width: fillWidth, backgroundColor: fillColor },
            ]}
          />
        )}

        {isLoading ? (
          <ActivityIndicator style={styles.spinner} color={textColor} />
        ) : (
          <Animated.Text
            style={[styles.label, { color: textColor }, labelStyle]}
          >
            {label}
          </Animated.Text>
        )}

        {!isLoading && (
          <Animated.View
            style={[styles.thumb, { transform: [{ translateX }] }]}
            {...panResponder.panHandlers}
          >
            {thumbIcon}
          </Animated.View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 20,
  },
  track: {
    height: THUMB_SIZE,
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    justifyContent: 'center',
  },
  fill: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    zIndex: 1,
  },
  label: {
    textAlign: 'center',
    zIndex: 0,
    fontWeight: 'bold',
  },
  thumb: {
    width: THUMB_SIZE,
    height: THUMB_SIZE,
    borderRadius: THUMB_SIZE / 2,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
    position: 'absolute',
    left: 0,
  },
  spinner: {
    alignSelf: 'center',
    zIndex: 0,
  },
});