# 📚 Componentes disponibles en mobile-core

---

## 🟦 SwipeButton

**Descripción:**  
Botón deslizable que permite confirmar una acción al desplazar un ícono horizontalmente. Ideal para confirmaciones importantes o acciones con intención clara.

**Palabras clave:**  
`botón`, `deslizar`, `swipe`, `confirmar`, `slide`, `loading`, `deslizable`, `thumb`, `spinner`, `acción`, `desliza para continuar`

**Props principales:**
- `onSwipeComplete: () => void` — función que se ejecuta al deslizar completamente
- `isLoading?: boolean` — muestra un spinner mientras se procesa la acción
- `disabled?: boolean` — desactiva la interacción
- `label?: string` — texto que aparece sobre el botón
- `thumbIcon?: ReactNode` — ícono que se desliza (puede ser un SVG o componente)
- `trackColor?: string` — color de fondo del botón
- `fillColor?: string` — color del rastro de progreso
- `textColor?: string` — color del texto
- `labelStyle?: TextStyle` — estilos adicionales al texto
- `containerStyle?: ViewStyle` — estilos del contenedor externo

**Ejemplo de uso:**

```tsx
<SwipeButton
  onSwipeComplete={() => console.log("Acción confirmada")}
  label="Desliza para pagar"
  isLoading={false}
  thumbIcon={<DoubleArrowRight />}
  trackColor="#222"
  fillColor="#4CAF50"
/>
```

**Ubicación:**  
`src/components/SwipeButton.tsx`

---